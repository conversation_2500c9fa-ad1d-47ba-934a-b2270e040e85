[package]
authors = ["Metaplex Developers <<EMAIL>>"]
description = "Metaplex Metadata"
edition = "2021"
license-file = "../../../LICENSE"
name = "token_metadata"
readme = "README.md"
repository = "https://github.com/metaplex-foundation/mpl-token-metadata"
version = "1.14.0"

[features]
no-entrypoint = []
padded = []
resize = []
serde-feature = ["serde", "serde_with"]
test-bpf = []

[dependencies]
arrayref = "0.3.6"
borsh = "0.9.3"
mpl-token-auth-rules = { version = "=1.4.3-beta.1", features = [
  "no-entrypoint",
] }
mpl-token-metadata-context-derive = { version = "0.3.0", path = "../macro" }
mpl-utils = { version = "0.3.6", features = ["spl-token"] }
num-derive = "0.3"
num-traits = "0.2"
serde = { version = "1.0.149", optional = true }
serde_with = { version = "1.14.0", optional = true }
shank = { version = "0.3.0" }
solana-program = ">= 1.14.13, < 1.17"
spl-associated-token-account = { version = ">= 1.1.3, < 3.0", features = [
  "no-entrypoint",
] }
spl-token-2022 = "0.8.0"
thiserror = "1.0"

[dev-dependencies]
async-trait = "0.1.64"
rmp-serde = "1.1.1"
rooster = { git = "https://github.com/metaplex-foundation/rooster", features = [
  "no-entrypoint",
] }
serde = { version = "1.0.147", features = ["derive"] }
solana-program-test = ">= 1.14.13, < 1.17"
solana-sdk = ">= 1.14.13, < 1.17"
spl-token = { version = "3.2.0", features = ["no-entrypoint"] }
test-case = "3.3.1"

[lib]
crate-type = ["cdylib", "lib"]
