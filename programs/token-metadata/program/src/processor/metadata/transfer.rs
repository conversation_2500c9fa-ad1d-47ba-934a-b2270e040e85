use std::fmt::Display;

use mpl_utils::{assert_signer, cmp_pubkeys, token::TokenTransferCheckedParams};
use solana_program::{
    account_info::AccountInfo,
    entrypoint::ProgramResult,
    program::invoke,
    program_error::ProgramError,
    program_option::COption,
    pubkey::Pubkey,
    system_program,
    sysvar::{self, instructions::get_instruction_relative},
};
use spl_token_2022::state::{Account, Mint};

use crate::{
    assertions::{assert_keys_equal, assert_owned_by, metadata::assert_holding_amount},
    error::MetadataError,
    instruction::{Context, Transfer, TransferArgs},
    pda::find_token_record_account,
    state::{
        AuthorityRequest, AuthorityResponse, AuthorityType, Key, Metadata, Operation,
        TokenDelegateRole, TokenMetadataAccount, TokenRecord, TokenStandard,
    },
    utils::{
        assert_token_program_matches_package, auth_rules_validate, clear_close_authority,
        close_program_account, create_token_record_account, frozen_transfer, unpack,
        validate_token, AuthRulesValidateParams, ClearCloseAuthorityParams,
    },
};

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, Debug, PartialEq, Eq)]
pub enum TransferScenario {
    Holder,
    TransferDelegate,
    SaleDelegate,
    MigrationDelegate,
}

impl Display for TransferScenario {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            Self::Holder => write!(f, "Owner"),
            Self::TransferDelegate => write!(f, "TransferDelegate"),
            Self::SaleDelegate => write!(f, "SaleDelegate"),
            Self::MigrationDelegate => write!(f, "MigrationDelegate"),
        }
    }
}

impl From<TransferScenario> for TokenDelegateRole {
    fn from(delegate: TransferScenario) -> Self {
        match delegate {
            TransferScenario::TransferDelegate => TokenDelegateRole::Transfer,
            TransferScenario::SaleDelegate => TokenDelegateRole::Sale,
            TransferScenario::MigrationDelegate => TokenDelegateRole::Migration,
            _ => panic!("Invalid delegate role"),
        }
    }
}

impl From<TokenDelegateRole> for TransferScenario {
    fn from(delegate: TokenDelegateRole) -> Self {
        match delegate {
            TokenDelegateRole::Transfer => TransferScenario::TransferDelegate,
            TokenDelegateRole::Sale => TransferScenario::SaleDelegate,
            TokenDelegateRole::Migration => TransferScenario::MigrationDelegate,
            _ => panic!("Invalid delegate role"),
        }
    }
}

pub fn transfer<'a>(
    program_id: &Pubkey,
    accounts: &'a [AccountInfo<'a>],
    args: TransferArgs,
) -> ProgramResult {
    let context = Transfer::to_context(accounts)?;

    match args {
        TransferArgs::V1 { .. } => transfer_v1(program_id, context, args),
    }
}

fn transfer_v1(program_id: &Pubkey, ctx: Context<Transfer>, args: TransferArgs) -> ProgramResult {
    let TransferArgs::V1 {
        authorization_data: auth_data,
        amount,
    } = args;

    if amount == 0 {
        return Err(MetadataError::InvalidAmount.into());
    }

    // Check signers

    // This authority must always be a signer, regardless of if it's the
    // actual token owner, a delegate or some other authority authorized
    // by a rule set.
    assert_signer(ctx.accounts.authority_info)?;

    // Assert program ownership.
    assert_owned_by(ctx.accounts.metadata_info, program_id)?;
    assert_owned_by(
        ctx.accounts.mint_info,
        ctx.accounts.spl_token_program_info.key,
    )?;
    assert_owned_by(
        ctx.accounts.token_info,
        ctx.accounts.spl_token_program_info.key,
    )?;
    if let Some(owner_token_record_info) = ctx.accounts.owner_token_record_info {
        assert_owned_by(owner_token_record_info, program_id)?;
    }
    if let Some(master_edition) = ctx.accounts.edition_info {
        assert_owned_by(master_edition, program_id)?;
    }
    if let Some(authorization_rules) = ctx.accounts.authorization_rules_info {
        assert_owned_by(authorization_rules, &mpl_token_auth_rules::ID)?;
    }

    // Deserialize metadata.
    let metadata = Metadata::from_account_info(ctx.accounts.metadata_info)?;

    // Check if the destination exists.
    if ctx.accounts.destination_info.data_is_empty() {
        // creating the associated token account
        invoke(
            &spl_associated_token_account::instruction::create_associated_token_account(
                ctx.accounts.payer_info.key,
                ctx.accounts.destination_owner_info.key,
                ctx.accounts.mint_info.key,
                ctx.accounts.spl_token_program_info.key,
            ),
            &[
                ctx.accounts.payer_info.clone(),
                ctx.accounts.destination_owner_info.clone(),
                ctx.accounts.mint_info.clone(),
                ctx.accounts.destination_info.clone(),
            ],
        )?;
    } else {
        let token = validate_token(
            ctx.accounts.mint_info,
            ctx.accounts.destination_info,
            Some(ctx.accounts.destination_owner_info),
            ctx.accounts.spl_token_program_info,
            metadata.token_standard,
            None, // we already checked the supply of the mint account
        )?;

        // validates that the close authority on the token is either None
        // or the master edition account for programmable assets

        if matches!(
            metadata.token_standard,
            Some(TokenStandard::ProgrammableNonFungible)
                | Some(TokenStandard::ProgrammableNonFungibleEdition)
        ) {
            if let COption::Some(close_authority) = token.close_authority {
                // the close authority must match the master edition if there is one set
                // on the token account
                if let Some(edition) = ctx.accounts.edition_info {
                    if close_authority != *edition.key {
                        return Err(MetadataError::InvalidCloseAuthority.into());
                    }
                } else {
                    return Err(MetadataError::MissingEditionAccount.into());
                };
            }
        }
    }

    // Check program IDs.

    assert_token_program_matches_package(ctx.accounts.spl_token_program_info)?;

    if ctx.accounts.spl_ata_program_info.key != &spl_associated_token_account::ID {
        return Err(ProgramError::IncorrectProgramId);
    }

    if ctx.accounts.system_program_info.key != &system_program::ID {
        return Err(ProgramError::IncorrectProgramId);
    }

    if ctx.accounts.sysvar_instructions_info.key != &sysvar::instructions::ID {
        return Err(ProgramError::IncorrectProgramId);
    }

    if let Some(auth_rules_program) = ctx.accounts.authorization_rules_program_info {
        if auth_rules_program.key != &mpl_token_auth_rules::ID {
            return Err(ProgramError::IncorrectProgramId);
        }
    }

    let mut is_wallet_to_wallet = false;

    // Must be the actual current owner of the token where
    // mint, token, owner and metadata accounts all match up.
    assert_holding_amount(
        &crate::ID,
        ctx.accounts.token_owner_info,
        ctx.accounts.metadata_info,
        &metadata,
        ctx.accounts.mint_info,
        ctx.accounts.token_info,
        amount,
    )?;

    let mint = unpack::<Mint>(&ctx.accounts.mint_info.data.borrow())?;
    let token_transfer_params = TokenTransferCheckedParams {
        mint: ctx.accounts.mint_info.clone(),
        source: ctx.accounts.token_info.clone(),
        destination: ctx.accounts.destination_info.clone(),
        amount,
        authority: ctx.accounts.authority_info.clone(),
        authority_signer_seeds: None,
        token_program: ctx.accounts.spl_token_program_info.clone(),
        decimals: mint.decimals,
    };

    let token_standard = metadata.token_standard;
    let token = unpack::<Account>(&ctx.accounts.token_info.try_borrow_data()?)?;

    let AuthorityResponse { authority_type, .. } =
        AuthorityType::get_authority_type(AuthorityRequest {
            authority: ctx.accounts.authority_info.key,
            update_authority: &metadata.update_authority,
            mint: ctx.accounts.mint_info.key,
            token: Some(ctx.accounts.token_info.key),
            token_account: Some(&token),
            token_record_info: ctx.accounts.owner_token_record_info,
            token_delegate_roles: vec![
                TokenDelegateRole::Sale,
                TokenDelegateRole::Transfer,
                TokenDelegateRole::LockedTransfer,
                TokenDelegateRole::Migration,
            ],
            ..Default::default()
        })?;

    match authority_type {
        AuthorityType::Holder => {
            // Wallet-to-wallet are currently exempt from auth rules so we need to check this and pass it into
            // the auth rules validator function.
            //
            // This only applies to Holder transfers as we cannot prove a delegate transfer is
            // from a proper system wallet.

            // If the program id of the current instruction is anything other than our program id
            // we know this is a CPI call from another program.
            let current_ix =
                get_instruction_relative(0, ctx.accounts.sysvar_instructions_info).unwrap();

            let is_cpi = !cmp_pubkeys(&current_ix.program_id, &crate::ID);

            // This can be replaced with a sys call to curve25519 once that feature activates.
            let wallets_are_system_program_owned =
                cmp_pubkeys(ctx.accounts.token_owner_info.owner, &system_program::ID)
                    && cmp_pubkeys(
                        ctx.accounts.destination_owner_info.owner,
                        &system_program::ID,
                    );

            // The only case where a transfer is wallet-to-wallet is if the wallets are both owned by
            // the system program and it's not a CPI call. Holders have to be signers so we can reject
            // malicious PDA signers owned by the system program by rejecting CPI calls here.
            //
            // Legitimate programs can use initialized PDAs or multiple instructions with a temp program-owned
            // PDA to go around this restriction for cases where they are passing through a proper system wallet
            // signer via an invoke call.
            is_wallet_to_wallet = !is_cpi && wallets_are_system_program_owned;
        }
        AuthorityType::TokenDelegate => {
            // the delegate has already being validated, but we need to validate
            // that it can transfer the required amount
            if token.delegated_amount < amount || token.amount < amount {
                return Err(MetadataError::InsufficientTokenBalance.into());
            }
        }
        _ => {
            if matches!(token_standard, Some(TokenStandard::ProgrammableNonFungible)) {
                return Err(MetadataError::InvalidAuthorityType.into());
            }

            // the authority must be either the token owner or a delegate for the
            // transfer to succeed
            let available_amount = if cmp_pubkeys(&token.owner, ctx.accounts.authority_info.key) {
                token.amount
            } else if COption::from(*ctx.accounts.authority_info.key) == token.delegate {
                token.delegated_amount
            } else {
                return Err(MetadataError::InvalidAuthorityType.into());
            };

            if available_amount < amount {
                return Err(MetadataError::InsufficientTokenBalance.into());
            }
        }
    }

    match token_standard {
        Some(TokenStandard::ProgrammableNonFungible)
        | Some(TokenStandard::ProgrammableNonFungibleEdition) => {
            // All pNFTs should have a token record passed in and existing.
            // The token delegate role may not be populated, however.
            let owner_token_record_info =
                if let Some(record_info) = ctx.accounts.owner_token_record_info {
                    record_info
                } else {
                    return Err(MetadataError::MissingTokenRecord.into());
                };

            let destination_token_record_info =
                if let Some(record_info) = ctx.accounts.destination_token_record_info {
                    record_info
                } else {
                    return Err(MetadataError::MissingTokenRecord.into());
                };

            let (pda_key, _) =
                find_token_record_account(ctx.accounts.mint_info.key, ctx.accounts.token_info.key);
            // validates the derivation
            assert_keys_equal(&pda_key, owner_token_record_info.key)?;

            let (new_pda_key, _) = find_token_record_account(
                ctx.accounts.mint_info.key,
                ctx.accounts.destination_info.key,
            );
            // validates the derivation
            assert_keys_equal(&new_pda_key, destination_token_record_info.key)?;

            // We need to check whether the destination token account has a delegate set. If it does,
            // we do not allow the transfer to proceed since we do not know the type of the delegate
            // to complete the information on the token record.
            let destination_token =
                unpack::<Account>(&ctx.accounts.destination_info.data.borrow())?;

            if let COption::Some(delegate) = destination_token.delegate {
                if destination_token_record_info.data_is_empty() {
                    return Err(MetadataError::DelegateAlreadyExists.into());
                }

                let destination_token_record =
                    TokenRecord::from_account_info(destination_token_record_info)?;

                if destination_token_record.delegate != Some(delegate) {
                    return Err(MetadataError::DelegateAlreadyExists.into());
                }
            }

            let owner_token_record = TokenRecord::from_account_info(owner_token_record_info)?;

            let is_sale_delegate = owner_token_record
                .delegate_role
                .map(|role| role == TokenDelegateRole::Sale)
                .unwrap_or(false);

            let scenario = match authority_type {
                AuthorityType::Holder => {
                    if is_sale_delegate {
                        return Err(MetadataError::OnlySaleDelegateCanTransfer.into());
                    }
                    TransferScenario::Holder
                }
                AuthorityType::TokenDelegate => {
                    let delegate_role = owner_token_record
                        .delegate_role
                        .ok_or(MetadataError::MissingDelegateRole)?;

                    if matches!(delegate_role, TokenDelegateRole::LockedTransfer) {
                        // locked transfer is a special case of the transfer so we proceed
                        // as a 'normal' transfer
                        TokenDelegateRole::Transfer.into()
                    } else {
                        delegate_role.into()
                    }
                }
                _ => return Err(MetadataError::InvalidTransferAuthority.into()),
            };

            // Build our auth rules params.
            let auth_rules_validate_params = AuthRulesValidateParams {
                mint_info: ctx.accounts.mint_info,
                owner_info: None,
                authority_info: Some(ctx.accounts.authority_info),
                source_info: Some(ctx.accounts.token_owner_info),
                destination_info: Some(ctx.accounts.destination_owner_info),
                programmable_config: metadata.programmable_config,
                amount,
                auth_data,
                auth_rules_info: ctx.accounts.authorization_rules_info,
                operation: Operation::Transfer { scenario },
                is_wallet_to_wallet,
                rule_set_revision: owner_token_record
                    .rule_set_revision
                    .map(|revision| revision as usize),
            };

            auth_rules_validate(auth_rules_validate_params)?;
            frozen_transfer(
                token_transfer_params,
                metadata.edition_nonce,
                ctx.accounts.edition_info,
            )?;

            let master_edition_info = ctx
                .accounts
                .edition_info
                .ok_or(MetadataError::MissingEditionAccount)?;

            clear_close_authority(ClearCloseAuthorityParams {
                token_info: ctx.accounts.token_info,
                mint_info: ctx.accounts.mint_info,
                token: &token,
                master_edition_info,
                authority_info: master_edition_info,
                spl_token_program_info: ctx.accounts.spl_token_program_info,
                edition_bump: metadata.edition_nonce,
            })?;

            // If the token record account for the destination owner doesn't exist,
            // we create it.
            if destination_token_record_info.data_is_empty() {
                create_token_record_account(
                    program_id,
                    destination_token_record_info,
                    ctx.accounts.mint_info,
                    ctx.accounts.destination_info,
                    ctx.accounts.payer_info,
                    ctx.accounts.system_program_info,
                )?;
            }

            // Don't close token record if it's a self transfer.
            if owner_token_record_info.key != destination_token_record_info.key {
                // If the transfer authority is the holder, we need to manually clear the
                // token delegate since it does not get cleared by the SPL token program
                // on transfer.
                if matches!(scenario, TransferScenario::Holder)
                    && owner_token_record.delegate.is_some()
                {
                    invoke(
                        &spl_token_2022::instruction::revoke(
                            ctx.accounts.spl_token_program_info.key,
                            ctx.accounts.token_info.key,
                            ctx.accounts.authority_info.key,
                            &[],
                        )?,
                        &[
                            ctx.accounts.token_info.clone(),
                            ctx.accounts.authority_info.clone(),
                        ],
                    )?;
                }

                // Close the source Token Record account, but do it after the CPI calls
                // so as to avoid Unbalanced Accounts errors due to the CPI context not knowing
                // about the manual lamport math done here.
                close_program_account(
                    owner_token_record_info,
                    ctx.accounts.payer_info,
                    Key::TokenRecord,
                )?;
            }
        }
        _ => mpl_utils::token::spl_token_transfer_checked(token_transfer_params).unwrap(),
    }

    Ok(())
}
