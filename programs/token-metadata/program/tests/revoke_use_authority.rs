#![cfg(feature = "test-bpf")]
pub mod utils;

use solana_program_test::*;
use solana_sdk::{
    signature::{Keypair, Signer},
    transaction::Transaction,
};
use token_metadata::{
    pda::find_use_authority_account,
    state::{Use<PERSON><PERSON><PERSON>tyRecord, UseMethod, Uses},
};
use utils::*;
mod revoke_use_authority {
    use borsh::BorshDeserialize;
    use token_metadata::pda::find_program_as_burner_account;

    use super::*;
    #[tokio::test]
    async fn success() {
        let mut context = program_test().start_with_context().await;
        let use_authority = Keypair::new();

        let test_meta = Metadata::new();
        test_meta
            .create_v3(
                &mut context,
                "Test".to_string(),
                "TST".to_string(),
                "uri".to_string(),
                None,
                10,
                false,
                None,
                Some(Uses {
                    use_method: UseMethod::Single,
                    total: 1,
                    remaining: 1,
                }),
                None,
            )
            .await
            .unwrap();

        let (record, _) =
            find_use_authority_account(&test_meta.mint.pubkey(), &use_authority.pubkey());
        let (burner, _) = find_program_as_burner_account();

        let approve_ix = token_metadata::instruction::approve_use_authority(
            token_metadata::ID,
            record,
            use_authority.pubkey(),
            context.payer.pubkey(),
            context.payer.pubkey(),
            test_meta.token.pubkey(),
            test_meta.pubkey,
            test_meta.mint.pubkey(),
            burner,
            1,
        );

        let approve_tx = Transaction::new_signed_with_payer(
            &[approve_ix],
            Some(&context.payer.pubkey()),
            &[&context.payer],
            context.last_blockhash,
        );

        context
            .banks_client
            .process_transaction(approve_tx)
            .await
            .unwrap();

        let account = get_account(&mut context, &record).await;
        let record_acct: UseAuthorityRecord =
            BorshDeserialize::deserialize(&mut &account.data[..]).unwrap();

        assert_eq!(record_acct.allowed_uses, 1);

        let revoke_ix = token_metadata::instruction::revoke_use_authority(
            token_metadata::ID,
            record,
            use_authority.pubkey(),
            context.payer.pubkey(),
            test_meta.token.pubkey(),
            test_meta.pubkey,
            test_meta.mint.pubkey(),
        );

        let revoke_tx = Transaction::new_signed_with_payer(
            &[revoke_ix],
            Some(&context.payer.pubkey()),
            &[&context.payer],
            context.last_blockhash,
        );

        context
            .banks_client
            .process_transaction(revoke_tx)
            .await
            .unwrap();

        let accountafter = context
            .banks_client
            .get_account(record)
            .await
            .expect("account not found");
        println!("{:?}", accountafter);
        assert!(accountafter.is_none());
    }
}
