{"name": "@metaplex-foundation/mpl-token-metadata", "version": "2.13.0", "contractVersion": "1.13.0", "description": "MPL Token Metadata JavaScript API.", "main": "dist/src/mpl-token-metadata.js", "types": "dist/src/mpl-token-metadata.d.ts", "scripts": {"check:publish-ready": "yarn build && yarn lint", "preversion": "yarn check:publish-ready", "postversion": "git add package.json && git commit -m \"chore: update $npm_package_name to v$npm_package_version\" && git tag $npm_package_name@$npm_package_version", "prepublishOnly": "yarn check:publish-ready", "postpublish": "git push origin && git push origin --tags", "build:docs": "typedoc", "build": "rimraf dist && tsc -p tsconfig.json", "api:gen": "DEBUG='(solita|rustbin):(info|error)' solita", "test": "cpr test/fixtures dist/test/fixtures -o && tape dist/test/*.js", "lint": "eslint \"{src,test}/**/*.ts\" --format stylish", "fix:lint": "yarn lint --fix", "prettier": "prettier \"{src,test}/**/*.ts\" --check", "fix:prettier": "prettier --write src/", "fix": "yarn fix:lint && yarn fix:prettier", "amman:start": "amman start", "amman:stop": "amman stop"}, "files": ["/dist/src"], "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org"}, "keywords": ["nft", "metaplex", "solana", "blockchain"], "homepage": "https://metaplex.com", "repository": "https://github.com/metaplex-foundation/metaplex-program-library.git", "author": "Metaplex Maintainers <<EMAIL>>", "license": "MIT", "dependencies": {"@metaplex-foundation/beet": "^0.7.1", "@metaplex-foundation/beet-solana": "^0.4.0", "@metaplex-foundation/cusper": "^0.0.2", "@solana/spl-token": "^0.3.6", "@solana/web3.js": "^1.66.2", "bn.js": "^5.2.0", "debug": "^4.3.4"}, "devDependencies": {"@metaplex-foundation/amman": "^0.12.0", "@metaplex-foundation/amman-client": "^0.2.2", "@metaplex-foundation/solita": "^0.19.3", "@metaplex-foundation/mpl-token-auth-rules": "^1.1.0", "@msgpack/msgpack": "^2.8.0", "@types/bn.js": "^5.1.1", "@types/debug": "^4.1.7", "@types/tape": "^4.13.2", "@typescript-eslint/eslint-plugin": "^5.44.0", "@typescript-eslint/parser": "^5.44.0", "cpr": "^3.0.1", "esbuild": "0.15.12", "esbuild-runner": "^2.2.1", "eslint": "^8.3.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.2.1", "prettier": "^2.5.1", "rimraf": "^3.0.2", "spok": "^1.4.3", "supports-color": "^9.2.3", "tape": "^5.5.3", "typedoc": "^0.23.16", "typescript": "^4.9.3"}}