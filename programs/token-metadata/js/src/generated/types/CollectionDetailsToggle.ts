/**
 * This code was GENERATED using the solita package.
 * Please DO NOT EDIT THIS FILE, instead rerun solita to update it or write a wrapper to add functionality.
 *
 * See: https://github.com/metaplex-foundation/solita
 */

import * as beet from '@metaplex-foundation/beet';
import { CollectionDetails, collectionDetailsBeet } from './CollectionDetails';
/**
 * This type is used to derive the {@link CollectionDetailsToggle} type as well as the de/serializer.
 * However don't refer to it in your code but use the {@link CollectionDetailsToggle} type instead.
 *
 * @category userTypes
 * @category enums
 * @category generated
 * @private
 */
export type CollectionDetailsToggleRecord = {
  None: void /* scalar variant */;
  Clear: void /* scalar variant */;
  Set: { fields: [CollectionDetails] };
};

/**
 * Union type respresenting the CollectionDetailsToggle data enum defined in Rust.
 *
 * NOTE: that it includes a `__kind` property which allows to narrow types in
 * switch/if statements.
 * Additionally `isCollectionDetailsToggle*` type guards are exposed below to narrow to a specific variant.
 *
 * @category userTypes
 * @category enums
 * @category generated
 */
export type CollectionDetailsToggle = beet.DataEnumKeyAsKind<CollectionDetailsToggleRecord>;

export const isCollectionDetailsToggleNone = (
  x: CollectionDetailsToggle,
): x is CollectionDetailsToggle & { __kind: 'None' } => x.__kind === 'None';
export const isCollectionDetailsToggleClear = (
  x: CollectionDetailsToggle,
): x is CollectionDetailsToggle & { __kind: 'Clear' } => x.__kind === 'Clear';
export const isCollectionDetailsToggleSet = (
  x: CollectionDetailsToggle,
): x is CollectionDetailsToggle & { __kind: 'Set' } => x.__kind === 'Set';

/**
 * @category userTypes
 * @category generated
 */
export const collectionDetailsToggleBeet = beet.dataEnum<CollectionDetailsToggleRecord>([
  ['None', beet.unit],
  ['Clear', beet.unit],
  [
    'Set',
    new beet.FixableBeetArgsStruct<CollectionDetailsToggleRecord['Set']>(
      [['fields', beet.tuple([collectionDetailsBeet])]],
      'CollectionDetailsToggleRecord["Set"]',
    ),
  ],
]) as beet.FixableBeet<CollectionDetailsToggle, CollectionDetailsToggle>;
