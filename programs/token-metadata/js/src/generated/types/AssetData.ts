/**
 * This code was GENERATED using the solita package.
 * Please DO NOT EDIT THIS FILE, instead rerun solita to update it or write a wrapper to add functionality.
 *
 * See: https://github.com/metaplex-foundation/solita
 */

import * as beet from '@metaplex-foundation/beet';
import * as web3 from '@solana/web3.js';
import * as beetSolana from '@metaplex-foundation/beet-solana';
import { Creator, creatorBeet } from './Creator';
import { TokenStandard, tokenStandardBeet } from './TokenStandard';
import { Collection, collectionBeet } from './Collection';
import { Uses, usesBeet } from './Uses';
import { CollectionDetails, collectionDetailsBeet } from './CollectionDetails';
export type AssetData = {
  name: string;
  symbol: string;
  uri: string;
  sellerFeeBasisPoints: number;
  creators: beet.COption<Creator[]>;
  primarySaleHappened: boolean;
  isMutable: boolean;
  tokenStandard: TokenStandard;
  collection: beet.COption<Collection>;
  uses: beet.COption<Uses>;
  collectionDetails: beet.COption<CollectionDetails>;
  ruleSet: beet.COption<web3.PublicKey>;
};

/**
 * @category userTypes
 * @category generated
 */
export const assetDataBeet = new beet.FixableBeetArgsStruct<AssetData>(
  [
    ['name', beet.utf8String],
    ['symbol', beet.utf8String],
    ['uri', beet.utf8String],
    ['sellerFeeBasisPoints', beet.u16],
    ['creators', beet.coption(beet.array(creatorBeet))],
    ['primarySaleHappened', beet.bool],
    ['isMutable', beet.bool],
    ['tokenStandard', tokenStandardBeet],
    ['collection', beet.coption(collectionBeet)],
    ['uses', beet.coption(usesBeet)],
    ['collectionDetails', beet.coption(collectionDetailsBeet)],
    ['ruleSet', beet.coption(beetSolana.publicKey)],
  ],
  'AssetData',
);
