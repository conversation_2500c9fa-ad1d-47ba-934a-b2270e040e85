/**
 * This code was GENERATED using the solita package.
 * Please DO NOT EDIT THIS FILE, instead rerun solita to update it or write a wrapper to add functionality.
 *
 * See: https://github.com/metaplex-foundation/solita
 */

import * as beet from '@metaplex-foundation/beet';
/**
 * This type is used to derive the {@link PrintArgs} type as well as the de/serializer.
 * However don't refer to it in your code but use the {@link PrintArgs} type instead.
 *
 * @category userTypes
 * @category enums
 * @category generated
 * @private
 */
export type PrintArgsRecord = {
  V1: { edition: beet.bignum };
};

/**
 * Union type respresenting the PrintArgs data enum defined in Rust.
 *
 * NOTE: that it includes a `__kind` property which allows to narrow types in
 * switch/if statements.
 * Additionally `isPrintArgs*` type guards are exposed below to narrow to a specific variant.
 *
 * @category userTypes
 * @category enums
 * @category generated
 */
export type PrintArgs = beet.DataEnumKeyAsKind<PrintArgsRecord>;

export const isPrintArgsV1 = (x: PrintArgs): x is PrintArgs & { __kind: 'V1' } => x.__kind === 'V1';

/**
 * @category userTypes
 * @category generated
 */
export const printArgsBeet = beet.dataEnum<PrintArgsRecord>([
  [
    'V1',
    new beet.BeetArgsStruct<PrintArgsRecord['V1']>(
      [['edition', beet.u64]],
      'PrintArgsRecord["V1"]',
    ),
  ],
]) as beet.FixableBeet<PrintArgs, PrintArgs>;
