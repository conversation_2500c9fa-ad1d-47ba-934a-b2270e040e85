/**
 * This code was GENERATED using the solita package.
 * Please DO NOT EDIT THIS FILE, instead rerun solita to update it or write a wrapper to add functionality.
 *
 * See: https://github.com/metaplex-foundation/solita
 */

import * as beet from '@metaplex-foundation/beet';
import { Collection, collectionBeet } from './Collection';
/**
 * This type is used to derive the {@link CollectionToggle} type as well as the de/serializer.
 * However don't refer to it in your code but use the {@link CollectionToggle} type instead.
 *
 * @category userTypes
 * @category enums
 * @category generated
 * @private
 */
export type CollectionToggleRecord = {
  None: void /* scalar variant */;
  Clear: void /* scalar variant */;
  Set: { fields: [Collection] };
};

/**
 * Union type respresenting the CollectionToggle data enum defined in Rust.
 *
 * NOTE: that it includes a `__kind` property which allows to narrow types in
 * switch/if statements.
 * Additionally `isCollectionToggle*` type guards are exposed below to narrow to a specific variant.
 *
 * @category userTypes
 * @category enums
 * @category generated
 */
export type CollectionToggle = beet.DataEnumKeyAsKind<CollectionToggleRecord>;

export const isCollectionToggleNone = (
  x: CollectionToggle,
): x is CollectionToggle & { __kind: 'None' } => x.__kind === 'None';
export const isCollectionToggleClear = (
  x: CollectionToggle,
): x is CollectionToggle & { __kind: 'Clear' } => x.__kind === 'Clear';
export const isCollectionToggleSet = (
  x: CollectionToggle,
): x is CollectionToggle & { __kind: 'Set' } => x.__kind === 'Set';

/**
 * @category userTypes
 * @category generated
 */
export const collectionToggleBeet = beet.dataEnum<CollectionToggleRecord>([
  ['None', beet.unit],
  ['Clear', beet.unit],
  [
    'Set',
    new beet.BeetArgsStruct<CollectionToggleRecord['Set']>(
      [['fields', beet.fixedSizeTuple([collectionBeet])]],
      'CollectionToggleRecord["Set"]',
    ),
  ],
]) as beet.FixableBeet<CollectionToggle, CollectionToggle>;
