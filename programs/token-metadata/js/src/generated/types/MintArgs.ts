/**
 * This code was GENERATED using the solita package.
 * Please DO NOT EDIT THIS FILE, instead rerun solita to update it or write a wrapper to add functionality.
 *
 * See: https://github.com/metaplex-foundation/solita
 */

import * as beet from '@metaplex-foundation/beet';
import { AuthorizationData, authorizationDataBeet } from './AuthorizationData';
/**
 * This type is used to derive the {@link MintArgs} type as well as the de/serializer.
 * However don't refer to it in your code but use the {@link MintArgs} type instead.
 *
 * @category userTypes
 * @category enums
 * @category generated
 * @private
 */
export type MintArgsRecord = {
  V1: { amount: beet.bignum; authorizationData: beet.COption<AuthorizationData> };
};

/**
 * Union type respresenting the MintArgs data enum defined in Rust.
 *
 * NOTE: that it includes a `__kind` property which allows to narrow types in
 * switch/if statements.
 * Additionally `isMintArgs*` type guards are exposed below to narrow to a specific variant.
 *
 * @category userTypes
 * @category enums
 * @category generated
 */
export type MintArgs = beet.DataEnumKeyAsKind<MintArgsRecord>;

export const isMintArgsV1 = (x: MintArgs): x is MintArgs & { __kind: 'V1' } => x.__kind === 'V1';

/**
 * @category userTypes
 * @category generated
 */
export const mintArgsBeet = beet.dataEnum<MintArgsRecord>([
  [
    'V1',
    new beet.FixableBeetArgsStruct<MintArgsRecord['V1']>(
      [
        ['amount', beet.u64],
        ['authorizationData', beet.coption(authorizationDataBeet)],
      ],
      'MintArgsRecord["V1"]',
    ),
  ],
]) as beet.FixableBeet<MintArgs, MintArgs>;
