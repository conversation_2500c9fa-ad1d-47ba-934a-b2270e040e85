/**
 * This code was GENERATED using the solita package.
 * Please DO NOT EDIT THIS FILE, instead rerun solita to update it or write a wrapper to add functionality.
 *
 * See: https://github.com/metaplex-foundation/solita
 */

import * as beet from '@metaplex-foundation/beet';
import { AuthorizationData, authorizationDataBeet } from './AuthorizationData';
/**
 * This type is used to derive the {@link UseArgs} type as well as the de/serializer.
 * However don't refer to it in your code but use the {@link UseArgs} type instead.
 *
 * @category userTypes
 * @category enums
 * @category generated
 * @private
 */
export type UseArgsRecord = {
  V1: { authorizationData: beet.COption<AuthorizationData> };
};

/**
 * Union type respresenting the UseArgs data enum defined in Rust.
 *
 * NOTE: that it includes a `__kind` property which allows to narrow types in
 * switch/if statements.
 * Additionally `isUseArgs*` type guards are exposed below to narrow to a specific variant.
 *
 * @category userTypes
 * @category enums
 * @category generated
 */
export type UseArgs = beet.DataEnumKeyAsKind<UseArgsRecord>;

export const isUseArgsV1 = (x: UseArgs): x is UseArgs & { __kind: 'V1' } => x.__kind === 'V1';

/**
 * @category userTypes
 * @category generated
 */
export const useArgsBeet = beet.dataEnum<UseArgsRecord>([
  [
    'V1',
    new beet.FixableBeetArgsStruct<UseArgsRecord['V1']>(
      [['authorizationData', beet.coption(authorizationDataBeet)]],
      'UseArgsRecord["V1"]',
    ),
  ],
]) as beet.FixableBeet<UseArgs, UseArgs>;
