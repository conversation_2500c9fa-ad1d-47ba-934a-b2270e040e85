/**
 * This code was GENERATED using the solita package.
 * Please DO NOT EDIT THIS FILE, instead rerun solita to update it or write a wrapper to add functionality.
 *
 * See: https://github.com/metaplex-foundation/solita
 */

import * as web3 from '@solana/web3.js';
import * as beet from '@metaplex-foundation/beet';
import * as beetSolana from '@metaplex-foundation/beet-solana';
/**
 * This type is used to derive the {@link RuleSetToggle} type as well as the de/serializer.
 * However don't refer to it in your code but use the {@link RuleSetToggle} type instead.
 *
 * @category userTypes
 * @category enums
 * @category generated
 * @private
 */
export type RuleSetToggleRecord = {
  None: void /* scalar variant */;
  Clear: void /* scalar variant */;
  Set: { fields: [web3.PublicKey] };
};

/**
 * Union type respresenting the RuleSetToggle data enum defined in Rust.
 *
 * NOTE: that it includes a `__kind` property which allows to narrow types in
 * switch/if statements.
 * Additionally `isRuleSetToggle*` type guards are exposed below to narrow to a specific variant.
 *
 * @category userTypes
 * @category enums
 * @category generated
 */
export type RuleSetToggle = beet.DataEnumKeyAsKind<RuleSetToggleRecord>;

export const isRuleSetToggleNone = (x: RuleSetToggle): x is RuleSetToggle & { __kind: 'None' } =>
  x.__kind === 'None';
export const isRuleSetToggleClear = (x: RuleSetToggle): x is RuleSetToggle & { __kind: 'Clear' } =>
  x.__kind === 'Clear';
export const isRuleSetToggleSet = (x: RuleSetToggle): x is RuleSetToggle & { __kind: 'Set' } =>
  x.__kind === 'Set';

/**
 * @category userTypes
 * @category generated
 */
export const ruleSetToggleBeet = beet.dataEnum<RuleSetToggleRecord>([
  ['None', beet.unit],
  ['Clear', beet.unit],
  [
    'Set',
    new beet.BeetArgsStruct<RuleSetToggleRecord['Set']>(
      [['fields', beet.fixedSizeTuple([beetSolana.publicKey])]],
      'RuleSetToggleRecord["Set"]',
    ),
  ],
]) as beet.FixableBeet<RuleSetToggle, RuleSetToggle>;
