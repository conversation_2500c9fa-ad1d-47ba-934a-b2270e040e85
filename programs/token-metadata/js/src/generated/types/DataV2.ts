/**
 * This code was GENERATED using the solita package.
 * Please DO NOT EDIT THIS FILE, instead rerun solita to update it or write a wrapper to add functionality.
 *
 * See: https://github.com/metaplex-foundation/solita
 */

import * as beet from '@metaplex-foundation/beet';
import { Creator, creatorBeet } from './Creator';
import { Collection, collectionBeet } from './Collection';
import { Uses, usesBeet } from './Uses';
export type DataV2 = {
  name: string;
  symbol: string;
  uri: string;
  sellerFeeBasisPoints: number;
  creators: beet.COption<Creator[]>;
  collection: beet.COption<Collection>;
  uses: beet.COption<Uses>;
};

/**
 * @category userTypes
 * @category generated
 */
export const dataV2Beet = new beet.FixableBeetArgsStruct<DataV2>(
  [
    ['name', beet.utf8String],
    ['symbol', beet.utf8String],
    ['uri', beet.utf8String],
    ['sellerFeeBasisPoints', beet.u16],
    ['creators', beet.coption(beet.array(creatorBeet))],
    ['collection', beet.coption(collectionBeet)],
    ['uses', beet.coption(usesBeet)],
  ],
  'DataV2',
);
