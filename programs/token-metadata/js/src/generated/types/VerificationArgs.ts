/**
 * This code was GENERATED using the solita package.
 * Please DO NOT EDIT THIS FILE, instead rerun solita to update it or write a wrapper to add functionality.
 *
 * See: https://github.com/metaplex-foundation/solita
 */

import * as beet from '@metaplex-foundation/beet';
/**
 * @category enums
 * @category generated
 */
export enum VerificationArgs {
  CreatorV1,
  CollectionV1,
}

/**
 * @category userTypes
 * @category generated
 */
export const verificationArgsBeet = beet.fixedScalarEnum(VerificationArgs) as beet.FixedSizeBeet<
  VerificationArgs,
  VerificationArgs
>;
