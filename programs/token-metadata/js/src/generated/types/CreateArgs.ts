/**
 * This code was GENERATED using the solita package.
 * Please DO NOT EDIT THIS FILE, instead rerun solita to update it or write a wrapper to add functionality.
 *
 * See: https://github.com/metaplex-foundation/solita
 */

import * as beet from '@metaplex-foundation/beet';
import { AssetData, assetDataBeet } from './AssetData';
import { PrintSupply, printSupplyBeet } from './PrintSupply';
/**
 * This type is used to derive the {@link CreateArgs} type as well as the de/serializer.
 * However don't refer to it in your code but use the {@link CreateArgs} type instead.
 *
 * @category userTypes
 * @category enums
 * @category generated
 * @private
 */
export type CreateArgsRecord = {
  V1: {
    assetData: AssetData;
    decimals: beet.COption<number>;
    printSupply: beet.COption<PrintSupply>;
  };
};

/**
 * Union type respresenting the CreateArgs data enum defined in Rust.
 *
 * NOTE: that it includes a `__kind` property which allows to narrow types in
 * switch/if statements.
 * Additionally `isCreateArgs*` type guards are exposed below to narrow to a specific variant.
 *
 * @category userTypes
 * @category enums
 * @category generated
 */
export type CreateArgs = beet.DataEnumKeyAsKind<CreateArgsRecord>;

export const isCreateArgsV1 = (x: CreateArgs): x is CreateArgs & { __kind: 'V1' } =>
  x.__kind === 'V1';

/**
 * @category userTypes
 * @category generated
 */
export const createArgsBeet = beet.dataEnum<CreateArgsRecord>([
  [
    'V1',
    new beet.FixableBeetArgsStruct<CreateArgsRecord['V1']>(
      [
        ['assetData', assetDataBeet],
        ['decimals', beet.coption(beet.u8)],
        ['printSupply', beet.coption(printSupplyBeet)],
      ],
      'CreateArgsRecord["V1"]',
    ),
  ],
]) as beet.FixableBeet<CreateArgs, CreateArgs>;
