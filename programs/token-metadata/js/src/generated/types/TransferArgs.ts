/**
 * This code was GENERATED using the solita package.
 * Please DO NOT EDIT THIS FILE, instead rerun solita to update it or write a wrapper to add functionality.
 *
 * See: https://github.com/metaplex-foundation/solita
 */

import * as beet from '@metaplex-foundation/beet';
import { AuthorizationData, authorizationDataBeet } from './AuthorizationData';
/**
 * This type is used to derive the {@link TransferArgs} type as well as the de/serializer.
 * However don't refer to it in your code but use the {@link TransferArgs} type instead.
 *
 * @category userTypes
 * @category enums
 * @category generated
 * @private
 */
export type TransferArgsRecord = {
  V1: { amount: beet.bignum; authorizationData: beet.COption<AuthorizationData> };
};

/**
 * Union type respresenting the TransferArgs data enum defined in Rust.
 *
 * NOTE: that it includes a `__kind` property which allows to narrow types in
 * switch/if statements.
 * Additionally `isTransferArgs*` type guards are exposed below to narrow to a specific variant.
 *
 * @category userTypes
 * @category enums
 * @category generated
 */
export type TransferArgs = beet.DataEnumKeyAsKind<TransferArgsRecord>;

export const isTransferArgsV1 = (x: TransferArgs): x is TransferArgs & { __kind: 'V1' } =>
  x.__kind === 'V1';

/**
 * @category userTypes
 * @category generated
 */
export const transferArgsBeet = beet.dataEnum<TransferArgsRecord>([
  [
    'V1',
    new beet.FixableBeetArgsStruct<TransferArgsRecord['V1']>(
      [
        ['amount', beet.u64],
        ['authorizationData', beet.coption(authorizationDataBeet)],
      ],
      'TransferArgsRecord["V1"]',
    ),
  ],
]) as beet.FixableBeet<TransferArgs, TransferArgs>;
