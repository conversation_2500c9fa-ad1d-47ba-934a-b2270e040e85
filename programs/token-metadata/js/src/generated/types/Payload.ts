/**
 * This code was GENERATED using the solita package.
 * Please DO NOT EDIT THIS FILE, instead rerun solita to update it or write a wrapper to add functionality.
 *
 * See: https://github.com/metaplex-foundation/solita
 */

import * as beet from '@metaplex-foundation/beet';
import { PayloadType, payloadTypeBeet } from './PayloadType';
export type Payload = {
  map: Map<string, PayloadType>;
};

/**
 * @category userTypes
 * @category generated
 */
export const payloadBeet = new beet.FixableBeetArgsStruct<Payload>(
  [['map', beet.map(beet.utf8String, payloadTypeBeet)]],
  'Payload',
);
