/**
 * This code was GENERATED using the solita package.
 * Please DO NOT EDIT THIS FILE, instead rerun solita to update it or write a wrapper to add functionality.
 *
 * See: https://github.com/metaplex-foundation/solita
 */

import * as beet from '@metaplex-foundation/beet';
import { DataV2, dataV2Beet } from './DataV2';
import { CollectionDetails, collectionDetailsBeet } from './CollectionDetails';
export type CreateMetadataAccountArgsV3 = {
  data: DataV2;
  isMutable: boolean;
  collectionDetails: beet.COption<CollectionDetails>;
};

/**
 * @category userTypes
 * @category generated
 */
export const createMetadataAccountArgsV3Beet =
  new beet.FixableBeetArgsStruct<CreateMetadataAccountArgsV3>(
    [
      ['data', dataV2Beet],
      ['isMutable', beet.bool],
      ['collectionDetails', beet.coption(collectionDetailsBeet)],
    ],
    'CreateMetadataAccountArgsV3',
  );
