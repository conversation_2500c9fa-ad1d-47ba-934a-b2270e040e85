/**
 * This code was GENERATED using the solita package.
 * Please DO NOT EDIT THIS FILE, instead rerun solita to update it or write a wrapper to add functionality.
 *
 * See: https://github.com/metaplex-foundation/solita
 */

import * as beet from '@metaplex-foundation/beet';
import { Payload, payloadBeet } from './Payload';
export type AuthorizationData = {
  payload: Payload;
};

/**
 * @category userTypes
 * @category generated
 */
export const authorizationDataBeet = new beet.FixableBeetArgsStruct<AuthorizationData>(
  [['payload', payloadBeet]],
  'AuthorizationData',
);
