/**
 * This code was GENERATED using the solita package.
 * Please DO NOT EDIT THIS FILE, instead rerun solita to update it or write a wrapper to add functionality.
 *
 * See: https://github.com/metaplex-foundation/solita
 */

import * as web3 from '@solana/web3.js';
import * as beet from '@metaplex-foundation/beet';
import * as beetSolana from '@metaplex-foundation/beet-solana';
export type Reservation = {
  address: web3.PublicKey;
  spotsRemaining: beet.bignum;
  totalSpots: beet.bignum;
};

/**
 * @category userTypes
 * @category generated
 */
export const reservationBeet = new beet.BeetArgsStruct<Reservation>(
  [
    ['address', beetSolana.publicKey],
    ['spotsRemaining', beet.u64],
    ['totalSpots', beet.u64],
  ],
  'Reservation',
);
