/**
 * This code was GENERATED using the solita package.
 * Please DO NOT EDIT THIS FILE, instead rerun solita to update it or write a wrapper to add functionality.
 *
 * See: https://github.com/metaplex-foundation/solita
 */

import * as web3 from '@solana/web3.js';
import * as beet from '@metaplex-foundation/beet';
import * as beetSolana from '@metaplex-foundation/beet-solana';
import { Data, dataBeet } from './Data';
import { CollectionToggle, collectionToggleBeet } from './CollectionToggle';
import { CollectionDetailsToggle, collectionDetailsToggleBeet } from './CollectionDetailsToggle';
import { UsesToggle, usesToggleBeet } from './UsesToggle';
import { RuleSetToggle, ruleSetToggleBeet } from './RuleSetToggle';
import { AuthorizationData, authorizationDataBeet } from './AuthorizationData';
import { TokenStandard, tokenStandardBeet } from './TokenStandard';
/**
 * This type is used to derive the {@link UpdateArgs} type as well as the de/serializer.
 * However don't refer to it in your code but use the {@link UpdateArgs} type instead.
 *
 * @category userTypes
 * @category enums
 * @category generated
 * @private
 */
export type UpdateArgsRecord = {
  V1: {
    newUpdateAuthority: beet.COption<web3.PublicKey>;
    data: beet.COption<Data>;
    primarySaleHappened: beet.COption<boolean>;
    isMutable: beet.COption<boolean>;
    collection: CollectionToggle;
    collectionDetails: CollectionDetailsToggle;
    uses: UsesToggle;
    ruleSet: RuleSetToggle;
    authorizationData: beet.COption<AuthorizationData>;
  };
  AsUpdateAuthorityV2: {
    newUpdateAuthority: beet.COption<web3.PublicKey>;
    data: beet.COption<Data>;
    primarySaleHappened: beet.COption<boolean>;
    isMutable: beet.COption<boolean>;
    collection: CollectionToggle;
    collectionDetails: CollectionDetailsToggle;
    uses: UsesToggle;
    ruleSet: RuleSetToggle;
    tokenStandard: beet.COption<TokenStandard>;
    authorizationData: beet.COption<AuthorizationData>;
  };
  AsAuthorityItemDelegateV2: {
    newUpdateAuthority: beet.COption<web3.PublicKey>;
    primarySaleHappened: beet.COption<boolean>;
    isMutable: beet.COption<boolean>;
    tokenStandard: beet.COption<TokenStandard>;
    authorizationData: beet.COption<AuthorizationData>;
  };
  AsCollectionDelegateV2: {
    collection: CollectionToggle;
    authorizationData: beet.COption<AuthorizationData>;
  };
  AsDataDelegateV2: {
    data: beet.COption<Data>;
    authorizationData: beet.COption<AuthorizationData>;
  };
  AsProgrammableConfigDelegateV2: {
    ruleSet: RuleSetToggle;
    authorizationData: beet.COption<AuthorizationData>;
  };
  AsDataItemDelegateV2: {
    data: beet.COption<Data>;
    authorizationData: beet.COption<AuthorizationData>;
  };
  AsCollectionItemDelegateV2: {
    collection: CollectionToggle;
    authorizationData: beet.COption<AuthorizationData>;
  };
  AsProgrammableConfigItemDelegateV2: {
    ruleSet: RuleSetToggle;
    authorizationData: beet.COption<AuthorizationData>;
  };
};

/**
 * Union type respresenting the UpdateArgs data enum defined in Rust.
 *
 * NOTE: that it includes a `__kind` property which allows to narrow types in
 * switch/if statements.
 * Additionally `isUpdateArgs*` type guards are exposed below to narrow to a specific variant.
 *
 * @category userTypes
 * @category enums
 * @category generated
 */
export type UpdateArgs = beet.DataEnumKeyAsKind<UpdateArgsRecord>;

export const isUpdateArgsV1 = (x: UpdateArgs): x is UpdateArgs & { __kind: 'V1' } =>
  x.__kind === 'V1';
export const isUpdateArgsAsUpdateAuthorityV2 = (
  x: UpdateArgs,
): x is UpdateArgs & { __kind: 'AsUpdateAuthorityV2' } => x.__kind === 'AsUpdateAuthorityV2';
export const isUpdateArgsAsAuthorityItemDelegateV2 = (
  x: UpdateArgs,
): x is UpdateArgs & { __kind: 'AsAuthorityItemDelegateV2' } =>
  x.__kind === 'AsAuthorityItemDelegateV2';
export const isUpdateArgsAsCollectionDelegateV2 = (
  x: UpdateArgs,
): x is UpdateArgs & { __kind: 'AsCollectionDelegateV2' } => x.__kind === 'AsCollectionDelegateV2';
export const isUpdateArgsAsDataDelegateV2 = (
  x: UpdateArgs,
): x is UpdateArgs & { __kind: 'AsDataDelegateV2' } => x.__kind === 'AsDataDelegateV2';
export const isUpdateArgsAsProgrammableConfigDelegateV2 = (
  x: UpdateArgs,
): x is UpdateArgs & { __kind: 'AsProgrammableConfigDelegateV2' } =>
  x.__kind === 'AsProgrammableConfigDelegateV2';
export const isUpdateArgsAsDataItemDelegateV2 = (
  x: UpdateArgs,
): x is UpdateArgs & { __kind: 'AsDataItemDelegateV2' } => x.__kind === 'AsDataItemDelegateV2';
export const isUpdateArgsAsCollectionItemDelegateV2 = (
  x: UpdateArgs,
): x is UpdateArgs & { __kind: 'AsCollectionItemDelegateV2' } =>
  x.__kind === 'AsCollectionItemDelegateV2';
export const isUpdateArgsAsProgrammableConfigItemDelegateV2 = (
  x: UpdateArgs,
): x is UpdateArgs & { __kind: 'AsProgrammableConfigItemDelegateV2' } =>
  x.__kind === 'AsProgrammableConfigItemDelegateV2';

/**
 * @category userTypes
 * @category generated
 */
export const updateArgsBeet = beet.dataEnum<UpdateArgsRecord>([
  [
    'V1',
    new beet.FixableBeetArgsStruct<UpdateArgsRecord['V1']>(
      [
        ['newUpdateAuthority', beet.coption(beetSolana.publicKey)],
        ['data', beet.coption(dataBeet)],
        ['primarySaleHappened', beet.coption(beet.bool)],
        ['isMutable', beet.coption(beet.bool)],
        ['collection', collectionToggleBeet],
        ['collectionDetails', collectionDetailsToggleBeet],
        ['uses', usesToggleBeet],
        ['ruleSet', ruleSetToggleBeet],
        ['authorizationData', beet.coption(authorizationDataBeet)],
      ],
      'UpdateArgsRecord["V1"]',
    ),
  ],

  [
    'AsUpdateAuthorityV2',
    new beet.FixableBeetArgsStruct<UpdateArgsRecord['AsUpdateAuthorityV2']>(
      [
        ['newUpdateAuthority', beet.coption(beetSolana.publicKey)],
        ['data', beet.coption(dataBeet)],
        ['primarySaleHappened', beet.coption(beet.bool)],
        ['isMutable', beet.coption(beet.bool)],
        ['collection', collectionToggleBeet],
        ['collectionDetails', collectionDetailsToggleBeet],
        ['uses', usesToggleBeet],
        ['ruleSet', ruleSetToggleBeet],
        ['tokenStandard', beet.coption(tokenStandardBeet)],
        ['authorizationData', beet.coption(authorizationDataBeet)],
      ],
      'UpdateArgsRecord["AsUpdateAuthorityV2"]',
    ),
  ],

  [
    'AsAuthorityItemDelegateV2',
    new beet.FixableBeetArgsStruct<UpdateArgsRecord['AsAuthorityItemDelegateV2']>(
      [
        ['newUpdateAuthority', beet.coption(beetSolana.publicKey)],
        ['primarySaleHappened', beet.coption(beet.bool)],
        ['isMutable', beet.coption(beet.bool)],
        ['tokenStandard', beet.coption(tokenStandardBeet)],
        ['authorizationData', beet.coption(authorizationDataBeet)],
      ],
      'UpdateArgsRecord["AsAuthorityItemDelegateV2"]',
    ),
  ],

  [
    'AsCollectionDelegateV2',
    new beet.FixableBeetArgsStruct<UpdateArgsRecord['AsCollectionDelegateV2']>(
      [
        ['collection', collectionToggleBeet],
        ['authorizationData', beet.coption(authorizationDataBeet)],
      ],
      'UpdateArgsRecord["AsCollectionDelegateV2"]',
    ),
  ],

  [
    'AsDataDelegateV2',
    new beet.FixableBeetArgsStruct<UpdateArgsRecord['AsDataDelegateV2']>(
      [
        ['data', beet.coption(dataBeet)],
        ['authorizationData', beet.coption(authorizationDataBeet)],
      ],
      'UpdateArgsRecord["AsDataDelegateV2"]',
    ),
  ],

  [
    'AsProgrammableConfigDelegateV2',
    new beet.FixableBeetArgsStruct<UpdateArgsRecord['AsProgrammableConfigDelegateV2']>(
      [
        ['ruleSet', ruleSetToggleBeet],
        ['authorizationData', beet.coption(authorizationDataBeet)],
      ],
      'UpdateArgsRecord["AsProgrammableConfigDelegateV2"]',
    ),
  ],

  [
    'AsDataItemDelegateV2',
    new beet.FixableBeetArgsStruct<UpdateArgsRecord['AsDataItemDelegateV2']>(
      [
        ['data', beet.coption(dataBeet)],
        ['authorizationData', beet.coption(authorizationDataBeet)],
      ],
      'UpdateArgsRecord["AsDataItemDelegateV2"]',
    ),
  ],

  [
    'AsCollectionItemDelegateV2',
    new beet.FixableBeetArgsStruct<UpdateArgsRecord['AsCollectionItemDelegateV2']>(
      [
        ['collection', collectionToggleBeet],
        ['authorizationData', beet.coption(authorizationDataBeet)],
      ],
      'UpdateArgsRecord["AsCollectionItemDelegateV2"]',
    ),
  ],

  [
    'AsProgrammableConfigItemDelegateV2',
    new beet.FixableBeetArgsStruct<UpdateArgsRecord['AsProgrammableConfigItemDelegateV2']>(
      [
        ['ruleSet', ruleSetToggleBeet],
        ['authorizationData', beet.coption(authorizationDataBeet)],
      ],
      'UpdateArgsRecord["AsProgrammableConfigItemDelegateV2"]',
    ),
  ],
]) as beet.FixableBeet<UpdateArgs, UpdateArgs>;
