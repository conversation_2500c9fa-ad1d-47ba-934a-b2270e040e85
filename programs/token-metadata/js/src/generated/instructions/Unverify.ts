/**
 * This code was GENERATED using the solita package.
 * Please DO NOT EDIT THIS FILE, instead rerun solita to update it or write a wrapper to add functionality.
 *
 * See: https://github.com/metaplex-foundation/solita
 */

import * as beet from '@metaplex-foundation/beet';
import * as web3 from '@solana/web3.js';
import { VerificationArgs, verificationArgsBeet } from '../types/VerificationArgs';

/**
 * @category Instructions
 * @category Unverify
 * @category generated
 */
export type UnverifyInstructionArgs = {
  verificationArgs: VerificationArgs;
};
/**
 * @category Instructions
 * @category Unverify
 * @category generated
 */
export const UnverifyStruct = new beet.BeetArgsStruct<
  UnverifyInstructionArgs & {
    instructionDiscriminator: number;
  }
>(
  [
    ['instructionDiscriminator', beet.u8],
    ['verificationArgs', verificationArgsBeet],
  ],
  'UnverifyInstructionArgs',
);
/**
 * Accounts required by the _Unverify_ instruction
 *
 * @property [**signer**] authority Creator to verify, collection (or metadata if parent burned) update authority or delegate
 * @property [] delegateRecord (optional) Delegate record PDA
 * @property [_writable_] metadata Metadata account
 * @property [] collectionMint (optional) Mint of the Collection
 * @property [_writable_] collectionMetadata (optional) Metadata Account of the Collection
 * @property [] sysvarInstructions Instructions sysvar account
 * @category Instructions
 * @category Unverify
 * @category generated
 */
export type UnverifyInstructionAccounts = {
  authority: web3.PublicKey;
  delegateRecord?: web3.PublicKey;
  metadata: web3.PublicKey;
  collectionMint?: web3.PublicKey;
  collectionMetadata?: web3.PublicKey;
  systemProgram?: web3.PublicKey;
  sysvarInstructions: web3.PublicKey;
};

export const unverifyInstructionDiscriminator = 53;

/**
 * Creates a _Unverify_ instruction.
 *
 * Optional accounts that are not provided default to the program ID since
 * this was indicated in the IDL from which this instruction was generated.
 *
 * @param accounts that will be accessed while the instruction is processed
 * @param args to provide as instruction data to the program
 *
 * @category Instructions
 * @category Unverify
 * @category generated
 */
export function createUnverifyInstruction(
  accounts: UnverifyInstructionAccounts,
  args: UnverifyInstructionArgs,
  programId = new web3.PublicKey('metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s'),
) {
  const [data] = UnverifyStruct.serialize({
    instructionDiscriminator: unverifyInstructionDiscriminator,
    ...args,
  });
  const keys: web3.AccountMeta[] = [
    {
      pubkey: accounts.authority,
      isWritable: false,
      isSigner: true,
    },
    {
      pubkey: accounts.delegateRecord ?? programId,
      isWritable: false,
      isSigner: false,
    },
    {
      pubkey: accounts.metadata,
      isWritable: true,
      isSigner: false,
    },
    {
      pubkey: accounts.collectionMint ?? programId,
      isWritable: false,
      isSigner: false,
    },
    {
      pubkey: accounts.collectionMetadata ?? programId,
      isWritable: accounts.collectionMetadata != null,
      isSigner: false,
    },
    {
      pubkey: accounts.systemProgram ?? web3.SystemProgram.programId,
      isWritable: false,
      isSigner: false,
    },
    {
      pubkey: accounts.sysvarInstructions,
      isWritable: false,
      isSigner: false,
    },
  ];

  const ix = new web3.TransactionInstruction({
    programId,
    keys,
    data,
  });
  return ix;
}
