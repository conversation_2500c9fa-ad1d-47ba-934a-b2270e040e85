/**
 * This code was GENERATED using the solita package.
 * Please DO NOT EDIT THIS FILE, instead rerun solita to update it or write a wrapper to add functionality.
 *
 * See: https://github.com/metaplex-foundation/solita
 */

import * as beet from '@metaplex-foundation/beet';
import * as web3 from '@solana/web3.js';

/**
 * @category Instructions
 * @category CreateEscrowAccount
 * @category generated
 */
export const CreateEscrowAccountStruct = new beet.BeetArgsStruct<{
  instructionDiscriminator: number;
}>([['instructionDiscriminator', beet.u8]], 'CreateEscrowAccountInstructionArgs');
/**
 * Accounts required by the _CreateEscrowAccount_ instruction
 *
 * @property [_writable_] escrow Escrow account
 * @property [_writable_] metadata Metadata account
 * @property [] mint Mint account
 * @property [] tokenAccount Token account of the token
 * @property [] edition Edition account
 * @property [_writable_, **signer**] payer Wallet paying for the transaction and new account
 * @property [] sysvarInstructions Instructions sysvar account
 * @property [**signer**] authority (optional) Authority/creator of the escrow account
 * @category Instructions
 * @category CreateEscrowAccount
 * @category generated
 */
export type CreateEscrowAccountInstructionAccounts = {
  escrow: web3.PublicKey;
  metadata: web3.PublicKey;
  mint: web3.PublicKey;
  tokenAccount: web3.PublicKey;
  edition: web3.PublicKey;
  payer: web3.PublicKey;
  systemProgram?: web3.PublicKey;
  sysvarInstructions: web3.PublicKey;
  authority?: web3.PublicKey;
};

export const createEscrowAccountInstructionDiscriminator = 38;

/**
 * Creates a _CreateEscrowAccount_ instruction.
 *
 * Optional accounts that are not provided will be omitted from the accounts
 * array passed with the instruction.
 * An optional account that is set cannot follow an optional account that is unset.
 * Otherwise an Error is raised.
 *
 * @param accounts that will be accessed while the instruction is processed
 * @category Instructions
 * @category CreateEscrowAccount
 * @category generated
 */
export function createCreateEscrowAccountInstruction(
  accounts: CreateEscrowAccountInstructionAccounts,
  programId = new web3.PublicKey('metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s'),
) {
  const [data] = CreateEscrowAccountStruct.serialize({
    instructionDiscriminator: createEscrowAccountInstructionDiscriminator,
  });
  const keys: web3.AccountMeta[] = [
    {
      pubkey: accounts.escrow,
      isWritable: true,
      isSigner: false,
    },
    {
      pubkey: accounts.metadata,
      isWritable: true,
      isSigner: false,
    },
    {
      pubkey: accounts.mint,
      isWritable: false,
      isSigner: false,
    },
    {
      pubkey: accounts.tokenAccount,
      isWritable: false,
      isSigner: false,
    },
    {
      pubkey: accounts.edition,
      isWritable: false,
      isSigner: false,
    },
    {
      pubkey: accounts.payer,
      isWritable: true,
      isSigner: true,
    },
    {
      pubkey: accounts.systemProgram ?? web3.SystemProgram.programId,
      isWritable: false,
      isSigner: false,
    },
    {
      pubkey: accounts.sysvarInstructions,
      isWritable: false,
      isSigner: false,
    },
  ];

  if (accounts.authority != null) {
    keys.push({
      pubkey: accounts.authority,
      isWritable: false,
      isSigner: true,
    });
  }

  const ix = new web3.TransactionInstruction({
    programId,
    keys,
    data,
  });
  return ix;
}
