/**
 * This code was GENERATED using the solita package.
 * Please DO NOT EDIT THIS FILE, instead rerun solita to update it or write a wrapper to add functionality.
 *
 * See: https://github.com/metaplex-foundation/solita
 */

import * as splToken from '@solana/spl-token';
import * as beet from '@metaplex-foundation/beet';
import * as web3 from '@solana/web3.js';

/**
 * @category Instructions
 * @category DeprecatedMintPrintingTokensViaToken
 * @category generated
 */
export const DeprecatedMintPrintingTokensViaTokenStruct = new beet.BeetArgsStruct<{
  instructionDiscriminator: number;
}>([['instructionDiscriminator', beet.u8]], 'DeprecatedMintPrintingTokensViaTokenInstructionArgs');
/**
 * Accounts required by the _DeprecatedMintPrintingTokensViaToken_ instruction
 *
 * @property [_writable_] destination Destination account
 * @property [_writable_] token Token account containing one time authorization token
 * @property [_writable_] oneTimePrintingAuthorizationMint One time authorization mint
 * @property [_writable_] printingMint Printing mint
 * @property [**signer**] burnAuthority Burn authority
 * @property [] metadata Metadata key (pda of ['metadata', program id, mint id])
 * @property [] masterEdition Master Edition V1 key (pda of ['metadata', program id, mint id, 'edition'])
 * @category Instructions
 * @category DeprecatedMintPrintingTokensViaToken
 * @category generated
 */
export type DeprecatedMintPrintingTokensViaTokenInstructionAccounts = {
  destination: web3.PublicKey;
  token: web3.PublicKey;
  oneTimePrintingAuthorizationMint: web3.PublicKey;
  printingMint: web3.PublicKey;
  burnAuthority: web3.PublicKey;
  metadata: web3.PublicKey;
  masterEdition: web3.PublicKey;
  tokenProgram?: web3.PublicKey;
  rent?: web3.PublicKey;
};

export const deprecatedMintPrintingTokensViaTokenInstructionDiscriminator = 8;

/**
 * Creates a _DeprecatedMintPrintingTokensViaToken_ instruction.
 *
 * @param accounts that will be accessed while the instruction is processed
 * @category Instructions
 * @category DeprecatedMintPrintingTokensViaToken
 * @category generated
 */
export function createDeprecatedMintPrintingTokensViaTokenInstruction(
  accounts: DeprecatedMintPrintingTokensViaTokenInstructionAccounts,
  programId = new web3.PublicKey('metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s'),
) {
  const [data] = DeprecatedMintPrintingTokensViaTokenStruct.serialize({
    instructionDiscriminator: deprecatedMintPrintingTokensViaTokenInstructionDiscriminator,
  });
  const keys: web3.AccountMeta[] = [
    {
      pubkey: accounts.destination,
      isWritable: true,
      isSigner: false,
    },
    {
      pubkey: accounts.token,
      isWritable: true,
      isSigner: false,
    },
    {
      pubkey: accounts.oneTimePrintingAuthorizationMint,
      isWritable: true,
      isSigner: false,
    },
    {
      pubkey: accounts.printingMint,
      isWritable: true,
      isSigner: false,
    },
    {
      pubkey: accounts.burnAuthority,
      isWritable: false,
      isSigner: true,
    },
    {
      pubkey: accounts.metadata,
      isWritable: false,
      isSigner: false,
    },
    {
      pubkey: accounts.masterEdition,
      isWritable: false,
      isSigner: false,
    },
    {
      pubkey: accounts.tokenProgram ?? splToken.TOKEN_PROGRAM_ID,
      isWritable: false,
      isSigner: false,
    },
    {
      pubkey: accounts.rent ?? web3.SYSVAR_RENT_PUBKEY,
      isWritable: false,
      isSigner: false,
    },
  ];

  const ix = new web3.TransactionInstruction({
    programId,
    keys,
    data,
  });
  return ix;
}
