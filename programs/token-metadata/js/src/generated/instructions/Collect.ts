/**
 * This code was GENERATED using the solita package.
 * Please DO NOT EDIT THIS FILE, instead rerun solita to update it or write a wrapper to add functionality.
 *
 * See: https://github.com/metaplex-foundation/solita
 */

import * as beet from '@metaplex-foundation/beet';
import * as web3 from '@solana/web3.js';

/**
 * @category Instructions
 * @category Collect
 * @category generated
 */
export const CollectStruct = new beet.BeetArgsStruct<{ instructionDiscriminator: number }>(
  [['instructionDiscriminator', beet.u8]],
  'CollectInstructionArgs',
);
/**
 * Accounts required by the _Collect_ instruction
 *
 * @property [**signer**] authority Authority to collect fees
 * @property [] pdaAccount PDA to retrieve fees from
 * @category Instructions
 * @category Collect
 * @category generated
 */
export type CollectInstructionAccounts = {
  authority: web3.PublicKey;
  pdaAccount: web3.PublicKey;
};

export const collectInstructionDiscriminator = 54;

/**
 * Creates a _Collect_ instruction.
 *
 * @param accounts that will be accessed while the instruction is processed
 * @category Instructions
 * @category Collect
 * @category generated
 */
export function createCollectInstruction(
  accounts: CollectInstructionAccounts,
  programId = new web3.PublicKey('metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s'),
) {
  const [data] = CollectStruct.serialize({
    instructionDiscriminator: collectInstructionDiscriminator,
  });
  const keys: web3.AccountMeta[] = [
    {
      pubkey: accounts.authority,
      isWritable: false,
      isSigner: true,
    },
    {
      pubkey: accounts.pdaAccount,
      isWritable: false,
      isSigner: false,
    },
  ];

  const ix = new web3.TransactionInstruction({
    programId,
    keys,
    data,
  });
  return ix;
}
