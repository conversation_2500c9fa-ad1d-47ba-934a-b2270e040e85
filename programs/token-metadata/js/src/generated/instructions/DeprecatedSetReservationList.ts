/**
 * This code was GENERATED using the solita package.
 * Please DO NOT EDIT THIS FILE, instead rerun solita to update it or write a wrapper to add functionality.
 *
 * See: https://github.com/metaplex-foundation/solita
 */

import * as beet from '@metaplex-foundation/beet';
import * as web3 from '@solana/web3.js';

/**
 * @category Instructions
 * @category DeprecatedSetReservationList
 * @category generated
 */
export const DeprecatedSetReservationListStruct = new beet.BeetArgsStruct<{
  instructionDiscriminator: number;
}>([['instructionDiscriminator', beet.u8]], 'DeprecatedSetReservationListInstructionArgs');
/**
 * Accounts required by the _DeprecatedSetReservationList_ instruction
 *
 * @property [_writable_] masterEdition Master Edition V1 key (pda of ['metadata', program id, mint id, 'edition'])
 * @property [_writable_] reservationList PDA for ReservationList of ['metadata', program id, master edition key, 'reservation', resource-key]
 * @property [**signer**] resource The resource you tied the reservation list too
 * @category Instructions
 * @category DeprecatedSetReservationList
 * @category generated
 */
export type DeprecatedSetReservationListInstructionAccounts = {
  masterEdition: web3.PublicKey;
  reservationList: web3.PublicKey;
  resource: web3.PublicKey;
};

export const deprecatedSetReservationListInstructionDiscriminator = 5;

/**
 * Creates a _DeprecatedSetReservationList_ instruction.
 *
 * @param accounts that will be accessed while the instruction is processed
 * @category Instructions
 * @category DeprecatedSetReservationList
 * @category generated
 */
export function createDeprecatedSetReservationListInstruction(
  accounts: DeprecatedSetReservationListInstructionAccounts,
  programId = new web3.PublicKey('metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s'),
) {
  const [data] = DeprecatedSetReservationListStruct.serialize({
    instructionDiscriminator: deprecatedSetReservationListInstructionDiscriminator,
  });
  const keys: web3.AccountMeta[] = [
    {
      pubkey: accounts.masterEdition,
      isWritable: true,
      isSigner: false,
    },
    {
      pubkey: accounts.reservationList,
      isWritable: true,
      isSigner: false,
    },
    {
      pubkey: accounts.resource,
      isWritable: false,
      isSigner: true,
    },
  ];

  const ix = new web3.TransactionInstruction({
    programId,
    keys,
    data,
  });
  return ix;
}
