[package]
name = "mpl-token-metadata-context-derive"
version = "0.3.0"
edition = "2021"
description = "Metaplex Metadata"
authors = ["Metaplex Developers <<EMAIL>>"]
repository = "https://github.com/metaplex-foundation/metaplex-program-library"
license-file = "../../../LICENSE"
readme = "README.md"

[lib]
proc-macro = true

[dependencies]
quote = "1.0.21"
syn = { version = "1.0.103", features = ["extra-traits", "full"] }
